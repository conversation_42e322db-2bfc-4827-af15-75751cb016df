import {Flex, Divider, Typography, Tooltip} from 'antd';
import {memo, MouseEvent, useMemo, ReactNode} from 'react';
import {But<PERSON>} from '@panda-design/components';
import cx from 'classnames';
import {MCPServerBase} from '@/types/mcp/mcp';
import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import MCPCard from '@/design/MCP/MCPCard';
import {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
import TagGroup from '@/components/MCP/TagGroup';
import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
import PublishInfo from '@/components/MCP/PublishInfo';
import SvgEye from '@/icons/mcp/Eye';
import {
    actionButtonHoverStyle,
    cardContentStyle,
    containerCss,
    DescriptionContainer,
    DescriptionText,
    departmentTextStyle,
    dividerStyle,
    EllipsisOverlay,
    formatCount,
    hoverActionsStyle,
    iconStyle,
    protocolTextStyle,
    statsContainerStyle,
} from './BaseMCPCard.styles';

interface Props {
    server: MCPServerBase;
    refresh: () => void;
    showDepartment?: boolean;
    workspaceId?: number;
    onCardClick: () => void;
    onViewCountClick: (e: MouseEvent) => void;
    onPlaygroundClick?: (e: MouseEvent) => void;
    renderActions?: () => ReactNode;
}

const BaseMCPCard = ({
    server,
    refresh,
    showDepartment = false,
    workspaceId,
    onCardClick,
    onViewCountClick,
    onPlaygroundClick,
    renderActions,
}: Props) => {
    const tags = useMemo(() => (server.labels ?? []).map((label, index) => ({
        id: label.id || index,
        label: label.labelValue,
    })), [server.labels]);

    return (
        <MCPCard vertical onClick={onCardClick} className={containerCss}>
            <Flex gap={14} align="center">
                <MCPServerAvatar icon={server.icon} />
                <Flex vertical justify="space-between" style={cardContentStyle} gap={4}>
                    <Typography.Title level={4} ellipsis>
                        {server.name}
                    </Typography.Title>
                    <Flex align="center" gap={12}>
                        <Typography.Text style={protocolTextStyle}>
                            {getServerTypeText(server.serverSourceType)} | {server.serverProtocolType}
                        </Typography.Text>
                    </Flex>
                </Flex>
            </Flex>
            <Tooltip title={server.description || '暂无描述'} placement="top">
                <DescriptionContainer>
                    <DescriptionText>{server.description || '暂无描述'}</DescriptionText>
                    <EllipsisOverlay />
                </DescriptionContainer>
            </Tooltip>
            {showDepartment && (
                <Typography.Text style={departmentTextStyle}>
                    {server.departmentName || '暂无部门信息'}
                </Typography.Text>
            )}
            <TagGroup
                labels={tags}
                color="light-purple"
                prefix={null}
                style={{flexShrink: 1, overflow: 'hidden'}}
                gap={4}
            />
            <Divider style={dividerStyle} />
            <Flex justify="space-between" align="center">
                <Flex align="center" gap={12}>
                    <Tooltip title="浏览量">
                        <Flex
                            align="center"
                            gap={4}
                            onClick={onViewCountClick}
                            className={statsContainerStyle}
                        >
                            <SvgEye style={iconStyle} />
                            {formatCount(server.viewCount)}
                        </Flex>
                    </Tooltip>
                </Flex>
                <PublishInfo
                    username={server.publishUser}
                    time={server.publishTime}
                />
            </Flex>
            <Flex align="center" justify="space-between" gap={10} className={`hover-actions ${hoverActionsStyle}`}>
                {renderActions ? renderActions() : (
                    <>
                        <MCPCollectButton
                            refresh={refresh}
                            favorite={server.favorite}
                            serverId={server.id}
                            className={cx(actionButtonHoverStyle)}
                            showText={false}
                            iconColor="#0083FF"
                        />
                        <MCPSubscribeButton
                            refresh={refresh}
                            workspaceId={workspaceId || server.workspaceId}
                            id={server.id}
                            className={cx(actionButtonHoverStyle)}
                            showText={false}
                            iconColor="#0083FF"
                        />
                        <Button type="primary" onClick={onPlaygroundClick}>
                            去MCP Playground使用
                        </Button>
                    </>
                )}
            </Flex>
        </MCPCard>
    );
};

export default memo(BaseMCPCard);
